<!DOCTYPE html>
  <html lang="en">
	<head>
	  <meta charset="UTF-8" />
	  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
	  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
	  <title>系统邮件</title>
	</head>
	<body>
	  <div class="email-template">
		<!-- <div class="name">Hi,#{staffName}！</div> -->
		<div class="info">
		  <div class="header">
			<img class="logoimage" src="https://hti-public-image-prd-**********.cos.ap-shanghai.myqcloud.com/share/newlogo.png" alt="hti" />
		  </div>
		  <div class="content">
			<h2 class="title">代理申请通过通知(个人代理-刘云)</h2>
			<div class="border"></div></br>
			<div class="desc">
			  <div>
          刘云(<PERSON>)，您好:</br>
          申请代理:个人代理-刘云</br>
          您提交的申请已审核通过，请您登录小程序完成合同签署
          以下为华通伙伴小程序的初始账号和密码:</br>
			  账号：#{account}</br>
			  密码：#{password}</br>
			  若您使用的是手机，请直接点击此链接打开小程序：<a href="https://u23ijweflkms">https://u23ijweflkms</a></br>
			  若您使用的是电脑查看邮件，请扫下方小程序码：</br>
			  </div>
			</div>
		  </div>
		</div>
	  </div>
	</body>
	<style>
	  body {
		margin: 0;
		padding: 0;
		width: 100%;
		height: 100vh;
		display: flex;
		align-items: center;
	  }

	  .email-template {
		width: 90%;
		margin: 0 auto;
		background-color: #f7faff;
		padding: 48px 100px 48px 100px;
		box-sizing: border-box;
	  }

	  .email-template .name {
		color: #333333;
		font-weight: 700;
		font-size: 18px;
		text-align: left;
		margin-bottom: 30px;
	  }

	  .info {
		width: 100%;
		height: auto;
		min-height: 510px;
		background: #fff;
	  }

	  .info .header {
		width: 100%;
		height: 180px;
		background: url('https://hti-public-image-prd-**********.cos.ap-shanghai.myqcloud.com/share/newbg.png') no-repeat;
		position: relative;
		background-size: 100% 180px;
		border-radius: 6px 6px 0px 0px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 30px;
		padding-right: 20px;
		box-sizing: border-box;
	  }
	  .info .header .logoimage {
		position: absolute;
		right: 20px;
		top: 20px;
	  }

	  .info .content {
		padding: 0 40px;
		box-sizing: border-box;
		padding-bottom: 30px;
	  }

	  .info .content .title {
		font-size: 20px;
		font-weight: bold;
		color: #393939;
		padding: 28px 0 25px 0;
		margin: 0;
		text-align: center;
	  }

	  .info .content .border {
		width: 100%;
		border-bottom: 1px solid #e8e8e8;
	  }

	  .info .content .desc {
		font-size: 16px;
		color: #666666;
		text-align: left;
		font-weight: 800;
		margin-top: 0;
		margin-bottom: 41px;
		line-height: 24px;
	  }

	  .info .content .desc div .desc-info {
		display: inline-block;
		width: 356px;
	  }

	  .link-info {
		margin-top: 25px;
		display: flex;
		justify-content: flex-end;
	  }

	  .link-info a {
		color: #009597;
	  }

	  .desc {
		width: 100%;
	  }

	  .desc div {
		line-height: 32px;
	  }

	  .orange {
		color: #ff6600;
	  }
	</style>
</html>